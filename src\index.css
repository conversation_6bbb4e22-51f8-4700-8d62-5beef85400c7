@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  
  body {
    @apply bg-neutral-950 text-neutral-100 font-sans antialiased;
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  html, body, #root {
    @apply h-full;
  }
}

@layer components {
  .chat-bubble {
    @apply rounded-lg px-4 py-2 text-sm max-w-md;
  }
  
  .chat-bubble-user {
    @apply bg-indigo-500 text-white;
  }
  
  .chat-bubble-assistant {
    @apply bg-neutral-800 text-neutral-100;
  }
  
  .sidebar-item {
    @apply flex gap-3 items-center w-full px-6 py-3 text-sm font-medium hover:bg-neutral-800 focus:bg-neutral-800 transition-colors;
  }
  
  .input-field {
    @apply bg-neutral-900 border border-neutral-800 rounded-lg px-4 py-3 text-sm placeholder-neutral-500 focus:ring-2 focus:ring-indigo-500 focus:border-transparent outline-none;
  }
  
  .btn-primary {
    @apply bg-indigo-500 hover:bg-indigo-600 text-white font-medium px-4 py-2 rounded-lg transition-colors;
  }
  
  .btn-secondary {
    @apply bg-neutral-800 hover:bg-neutral-700 text-neutral-100 font-medium px-4 py-2 rounded-lg transition-colors;
  }
  
  .btn-ghost {
    @apply hover:bg-neutral-800 text-neutral-100 p-2 rounded-lg transition-colors;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: #525252;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #737373;
}

/* Selection */
::selection {
  background: rgba(99, 102, 241, 0.6);
}

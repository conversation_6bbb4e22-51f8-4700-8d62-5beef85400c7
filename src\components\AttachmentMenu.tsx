import React, { useState, useRef, useEffect } from 'react'
import { FileText, Image, X } from './Icons'

interface AttachmentMenuProps {
  isOpen: boolean
  onClose: () => void
  onFileSelect: () => void
  onImageSelect: () => void
  anchorRef: React.RefObject<HTMLElement>
}

const AttachmentMenu: React.FC<AttachmentMenuProps> = ({
  isOpen,
  onClose,
  onFileSelect,
  onImageSelect,
  anchorRef
}) => {
  const menuRef = useRef<HTMLDivElement>(null)
  const [position, setPosition] = useState({ top: 0, left: 0 })

  useEffect(() => {
    if (isOpen && anchorRef.current && menuRef.current) {
      const anchorRect = anchorRef.current.getBoundingClientRect()
      const menuRect = menuRef.current.getBoundingClientRect()
      
      // Position menu above the anchor button
      const top = anchorRect.top - menuRect.height - 8
      const left = anchorRect.left
      
      setPosition({ top, left })
    }
  }, [isOpen, anchorRef])

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose()
      }
    }

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      document.addEventListener('keydown', handleEscape)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
      document.removeEventListener('keydown', handleEscape)
    }
  }, [isOpen, onClose])

  if (!isOpen) return null

  return (
    <>
      {/* Backdrop */}
      <div className="fixed inset-0 z-40" onClick={onClose} />
      
      {/* Menu */}
      <div
        ref={menuRef}
        className="fixed z-50 bg-neutral-800 border border-neutral-700 rounded-lg shadow-lg backdrop-blur-lg min-w-[160px]"
        style={{
          top: position.top,
          left: position.left,
        }}
      >
        <div className="p-2">
          {/* Files option */}
          <button
            onClick={() => {
              onFileSelect()
              onClose()
            }}
            className="w-full flex items-center gap-3 px-3 py-2 text-sm text-neutral-200 hover:bg-neutral-700 rounded-md transition-colors"
          >
            <FileText className="h-4 w-4 text-neutral-400" />
            <span>Files</span>
          </button>

          {/* Images option */}
          <button
            onClick={() => {
              onImageSelect()
              onClose()
            }}
            className="w-full flex items-center gap-3 px-3 py-2 text-sm text-neutral-200 hover:bg-neutral-700 rounded-md transition-colors"
          >
            <Image className="h-4 w-4 text-neutral-400" />
            <span>Images</span>
          </button>
        </div>
      </div>
    </>
  )
}

export default AttachmentMenu

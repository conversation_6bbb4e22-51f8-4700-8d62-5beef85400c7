import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowLeft } from '../components/Icons';

const HistoryPage: React.FC = () => {
  return (
    <div className="flex-1 flex flex-col h-full bg-neutral-950 text-neutral-100">
      <header className="flex-shrink-0 h-16 flex items-center gap-3 px-4 md:px-8 border-b border-neutral-800 bg-neutral-900/60 backdrop-blur-lg">
        <Link to="/" className="text-neutral-400 hover:text-white">
          <ArrowLeft className="h-5 w-5" />
        </Link>
        <h2 className="text-lg font-semibold tracking-tight leading-none">Conversation History</h2>
      </header>
      <main className="flex-1 overflow-y-auto px-4 md:px-8 py-6 min-h-0">
        <div className="max-w-4xl mx-auto space-y-6">
          <p className="text-neutral-400">This is the history page. More features coming soon!</p>
          {/* TODO: Implement full conversation history browsing */}
        </div>
      </main>
    </div>
  );
};

export default HistoryPage;

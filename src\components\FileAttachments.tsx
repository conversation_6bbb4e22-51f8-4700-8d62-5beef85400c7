import React from 'react'
import { FileRecord } from '../types'
import { X, File, Image, FileText, FileSpreadsheet, Presentation } from './Icons'

interface FileAttachmentsProps {
  attachedFiles: FileRecord[]
  onRemoveFile: (fileId: string) => void
}

const FileAttachments: React.FC<FileAttachmentsProps> = ({ attachedFiles, onRemoveFile }) => {
  if (attachedFiles.length === 0) return null

  const getFileIcon = (fileType: string, mimeType?: string) => {
    switch (fileType) {
      case 'image':
        return <Image className="w-4 h-4 text-blue-400" />
      case 'pdf':
        return <FileText className="w-4 h-4 text-red-400" />
      case 'word':
        return <FileText className="w-4 h-4 text-blue-600" />
      case 'excel':
        return <FileSpreadsheet className="w-4 h-4 text-green-600" />
      case 'powerpoint':
        return <Presentation className="w-4 h-4 text-orange-600" />
      default:
        return <File className="w-4 h-4 text-gray-400" />
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
  }

  return (
    <div className="mb-3 p-3 bg-neutral-800/50 rounded-lg border border-neutral-700">
      <div className="flex items-center gap-2 mb-2">
        <File className="w-4 h-4 text-neutral-400" />
        <span className="text-sm text-neutral-400">
          {attachedFiles.length} file{attachedFiles.length > 1 ? 's' : ''} attached
        </span>
      </div>
      
      <div className="space-y-2">
        {attachedFiles.map((file) => (
          <div
            key={file.id}
            className="flex items-center gap-3 p-2 bg-neutral-900/50 rounded border border-neutral-600 group hover:border-neutral-500 transition-colors"
          >
            {getFileIcon(file.file_type, file.mime_type)}
            
            <div className="flex-1 min-w-0">
              <div className="text-sm text-white truncate">
                {file.filename}
              </div>
              <div className="text-xs text-neutral-400">
                {formatFileSize(file.file_size)} • {file.file_type}
                {file.extracted_content && (
                  <span className="ml-2 text-green-400">✓ Processed</span>
                )}
              </div>
            </div>
            
            <button
              onClick={() => onRemoveFile(file.id)}
              className="opacity-0 group-hover:opacity-100 p-1 hover:bg-neutral-700 rounded transition-all"
              title="Remove file"
            >
              <X className="w-4 h-4 text-neutral-400 hover:text-red-400" />
            </button>
          </div>
        ))}
      </div>
    </div>
  )
}

export default FileAttachments

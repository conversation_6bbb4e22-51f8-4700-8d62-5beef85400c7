import React, { useState, useEffect } from 'react'
import { X, Search, FileText, Image, File, Folder } from './Icons'
import { FileRecord } from '../types'

interface FilePickerProps {
  isOpen: boolean
  onClose: () => void
  onFileSelect: (files: FileRecord[]) => void
  mode: 'files' | 'images'
}

const FilePicker: React.FC<FilePickerProps> = ({
  isOpen,
  onClose,
  onFileSelect,
  mode
}) => {
  const [files, setFiles] = useState<FileRecord[]>([])
  const [filteredFiles, setFilteredFiles] = useState<FileRecord[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedFiles, setSelectedFiles] = useState<Set<string>>(new Set())
  const [isLoading, setIsLoading] = useState(false)
  const [chatloPath, setChatloPath] = useState('')

  useEffect(() => {
    if (isOpen) {
      loadChatloPath()
      // Only load files when actually needed
      loadAndFilterFiles()
    }
  }, [isOpen, searchQuery, mode]) // React to search and mode changes

  const loadAndFilterFiles = async () => {
    setIsLoading(true)
    try {
      if (window.electronAPI?.files) {
        let results: FileRecord[]

        // Use optimized search with higher limit for file picker
        results = await window.electronAPI.files.searchFiles(searchQuery, 50)

        // Filter by mode (client-side for better UX)
        let filtered = results
        if (mode === 'images') {
          filtered = results.filter(file => file.file_type === 'image')
        } else {
          filtered = results.filter(file => file.file_type !== 'image')
        }

        setFiles(results) // Keep original for reference
        setFilteredFiles(filtered)
      }
    } catch (error) {
      console.error('Error loading files:', error)
      setFilteredFiles([])
    } finally {
      setIsLoading(false)
    }
  }

  const loadChatloPath = async () => {
    try {
      if (window.electronAPI?.files) {
        const path = await window.electronAPI.files.getChatloFolderPath()
        setChatloPath(path)
      }
    } catch (error) {
      console.error('Error loading Chatlo path:', error)
    }
  }

  const toggleFileSelection = (fileId: string) => {
    const newSelection = new Set(selectedFiles)
    if (newSelection.has(fileId)) {
      newSelection.delete(fileId)
    } else {
      newSelection.add(fileId)
    }
    setSelectedFiles(newSelection)
  }

  const handleSelectFiles = () => {
    const selectedFileRecords = files.filter(file => selectedFiles.has(file.id))
    onFileSelect(selectedFileRecords)
    onClose()
  }

  const handleBrowseFiles = async () => {
    try {
      if (window.electronAPI?.files) {
        const result = await window.electronAPI.files.showOpenDialog({
          title: mode === 'images' ? 'Select Images' : 'Select Files',
          properties: ['openFile', 'multiSelections'],
          filters: mode === 'images' 
            ? [
                { name: 'Images', extensions: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'] },
                { name: 'All Files', extensions: ['*'] }
              ]
            : [
                { name: 'Documents', extensions: ['pdf', 'doc', 'docx', 'txt', 'md'] },
                { name: 'Spreadsheets', extensions: ['xls', 'xlsx', 'csv'] },
                { name: 'Presentations', extensions: ['ppt', 'pptx'] },
                { name: 'All Files', extensions: ['*'] }
              ]
        })

        if (!result.canceled && result.filePaths.length > 0) {
          // Copy files to uploads folder and index them
          const copiedFiles: FileRecord[] = []
          for (const filePath of result.filePaths) {
            try {
              const copiedPath = await window.electronAPI.files.copyFileToUploads(filePath)
              const fileId = await window.electronAPI.files.indexFile(copiedPath)
              if (fileId) {
                // Reload files to get the new file
                await loadFiles()
              }
            } catch (error) {
              console.error('Error copying file:', error)
            }
          }
        }
      }
    } catch (error) {
      console.error('Error browsing files:', error)
    }
  }

  const getFileIcon = (fileType: string) => {
    switch (fileType) {
      case 'image':
        return <Image className="h-4 w-4 text-blue-400" />
      case 'pdf':
        return <FileText className="h-4 w-4 text-red-400" />
      case 'word':
        return <FileText className="h-4 w-4 text-blue-600" />
      case 'excel':
        return <FileText className="h-4 w-4 text-green-600" />
      case 'powerpoint':
        return <FileText className="h-4 w-4 text-orange-600" />
      case 'text':
      case 'markdown':
        return <FileText className="h-4 w-4 text-neutral-400" />
      default:
        return <File className="h-4 w-4 text-neutral-400" />
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-neutral-900 border border-neutral-800 rounded-lg shadow-xl w-full max-w-4xl h-[80vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-neutral-800">
          <div>
            <h2 className="text-xl font-semibold">
              {mode === 'images' ? 'Select Images' : 'Select Files'}
            </h2>
            <p className="text-sm text-neutral-400 mt-1">
              Choose files from your Chatlo folder or browse for new ones
            </p>
          </div>
          <button
            onClick={onClose}
            className="h-8 w-8 flex items-center justify-center rounded-lg hover:bg-neutral-800 transition-colors"
          >
            <X className="h-4 w-4" />
          </button>
        </div>

        {/* Search and Browse */}
        <div className="p-6 border-b border-neutral-800">
          <div className="flex gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-neutral-500" />
              <input
                type="text"
                placeholder="Search files..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full bg-neutral-800 border border-neutral-700 rounded-lg pl-10 pr-4 py-2 text-sm focus:ring-2 focus:ring-indigo-500 focus:border-transparent outline-none"
              />
            </div>
            <button
              onClick={handleBrowseFiles}
              className="px-4 py-2 bg-indigo-500 hover:bg-indigo-600 text-white rounded-lg text-sm font-medium transition-colors"
            >
              Browse Files
            </button>
          </div>
        </div>

        {/* File List */}
        <div className="flex-1 overflow-y-auto p-6">
          {isLoading ? (
            <div className="flex items-center justify-center h-32">
              <div className="text-neutral-400">Loading files...</div>
            </div>
          ) : filteredFiles.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-32 text-neutral-400">
              <Folder className="h-8 w-8 mb-2" />
              <p>No {mode === 'images' ? 'images' : 'files'} found</p>
              <p className="text-sm mt-1">Try browsing for files or check your Chatlo folder</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredFiles.map((file) => (
                <div
                  key={file.id}
                  onClick={() => toggleFileSelection(file.id)}
                  className={`
                    p-4 border rounded-lg cursor-pointer transition-all
                    ${selectedFiles.has(file.id)
                      ? 'border-indigo-500 bg-indigo-500/10'
                      : 'border-neutral-700 hover:border-neutral-600 hover:bg-neutral-800/50'
                    }
                  `}
                >
                  <div className="flex items-start gap-3">
                    {getFileIcon(file.file_type)}
                    <div className="flex-1 min-w-0">
                      <h3 className="font-medium text-sm truncate">{file.filename}</h3>
                      <p className="text-xs text-neutral-500 mt-1">
                        {formatFileSize(file.file_size)} • {file.file_type}
                      </p>
                      <p className="text-xs text-neutral-600 mt-1">
                        {new Date(file.updated_at).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-neutral-800 flex items-center justify-between">
          <div className="text-sm text-neutral-400">
            {selectedFiles.size} file{selectedFiles.size !== 1 ? 's' : ''} selected
          </div>
          <div className="flex gap-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-neutral-400 hover:text-neutral-200 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleSelectFiles}
              disabled={selectedFiles.size === 0}
              className={`
                px-4 py-2 rounded-lg text-sm font-medium transition-colors
                ${selectedFiles.size > 0
                  ? 'bg-indigo-500 hover:bg-indigo-600 text-white'
                  : 'bg-neutral-700 text-neutral-500 cursor-not-allowed'
                }
              `}
            >
              Select Files
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default FilePicker

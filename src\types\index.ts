export interface Conversation {
  id: string
  title: string
  is_pinned: 0 | 1
  created_at: string
  updated_at: string
}

export interface Message {
  id: string
  conversation_id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  model?: string
  is_pinned?: 0 | 1
  created_at: string
  attachments?: FileAttachment[]
}

export interface FileRecord {
  id: string
  filename: string
  filepath: string
  file_type: string
  file_size: number
  content_hash: string
  extracted_content?: string
  metadata?: string // JSON
  created_at: string
  updated_at: string
}

export interface FileAttachment {
  id: string
  message_id: string
  file_id: string
  attachment_type: 'attachment' | 'reference'
  created_at: string
  file?: FileRecord // Populated when needed
}

export interface OpenRouterModel {
  id: string
  name: string
  description?: string
  context_length: number
  pricing: {
    prompt: string
    completion: string
  }
  top_provider: {
    max_completion_tokens?: number
  }
  // Enhanced properties for better categorization
  architecture?: {
    modality?: string
    tokenizer?: string
    instruct_type?: string
  }
  per_request_limits?: {
    prompt_tokens?: string
    completion_tokens?: string
  }
}

export interface ChatCompletionRequest {
  model: string
  messages: Array<{
    role: 'user' | 'assistant' | 'system'
    content: string
  }>
  temperature?: number
  max_tokens?: number
  stream?: boolean
  // Enhanced sampling controls
  top_p?: number
  top_k?: number
  frequency_penalty?: number
  presence_penalty?: number
  stop?: string[]
}

export interface Settings {
  openRouterApiKey?: string
  selectedModel?: string
  temperature?: number
  maxTokens?: number
  theme?: 'dark' | 'light'
  // Enhanced model configuration
  topP?: number
  topK?: number
  frequencyPenalty?: number
  presencePenalty?: number
  systemPrompt?: string
  // Model selection preferences
  favoriteModels?: string[]
  modelFilter?: 'all' | 'free' | 'flagship' | 'reasoning' | 'code' | 'vision'
}

export interface UpdateInfo {
  version: string
  releaseDate: string
  releaseName?: string
  releaseNotes?: string
}

export interface UpdateCheckResult {
  available: boolean
  message?: string
  error?: string
}

export interface UpdateDownloadResult {
  success: boolean
  error?: string
  message?: string
}

export interface UpdateProgress {
  percent: number
  bytesPerSecond: number
  total: number
  transferred: number
}

// Model categorization and filtering types
export interface ModelCategory {
  id: string
  name: string
  description: string
  filter: (model: OpenRouterModel) => boolean
  icon?: string
}

export interface ModelPreset {
  name: string
  description: string
  temperature: number
  topP: number
  topK: number
  maxTokens?: number
}

export interface EnhancedModelInfo extends OpenRouterModel {
  isFree: boolean
  isFlagship: boolean
  isReasoning: boolean
  isCode: boolean
  isVision: boolean
  provider: string
  maxTokensSupported: number
}

// Electron API types
declare global {
  interface Window {
    electronAPI: {
      db: {
        getConversations: () => Promise<Conversation[]>
        getConversation: (id: string) => Promise<Conversation | null>
        createConversation: (title: string) => Promise<string>
        updateConversation: (id: string, title: string) => Promise<void>
        deleteConversation: (id: string) => Promise<void>
        addMessage: (conversationId: string, message: Omit<Message, 'id' | 'created_at'>) => Promise<string>
        getMessages: (conversationId: string) => Promise<Message[]>
      }
      settings: {
        get: (key: string) => Promise<any>
        set: (key: string, value: any) => Promise<void>
      }
      files: {
        getChatloFolderPath: () => Promise<string>
        getIndexedFiles: () => Promise<FileRecord[]>
        searchFiles: (query: string) => Promise<FileRecord[]>
        indexFile: (filePath: string) => Promise<string | null>
        indexAllFiles: () => Promise<void>
        copyFileToUploads: (sourcePath: string, filename?: string) => Promise<string>
        saveContentAsFile: (content: string, filename: string, subfolder?: string) => Promise<string>
        deleteFile: (fileId: string) => Promise<boolean>
        getFileContent: (filePath: string) => Promise<Buffer | null>
        fileExists: (filePath: string) => Promise<boolean>
        showOpenDialog: (options: any) => Promise<any>
        showSaveDialog: (options: any) => Promise<any>
      }
      updater: {
        checkForUpdates: () => Promise<UpdateCheckResult>
        downloadAndInstall: () => Promise<UpdateDownloadResult>
        onCheckingForUpdate: (callback: () => void) => void
        onUpdateAvailable: (callback: (info: UpdateInfo) => void) => void
        onUpdateNotAvailable: (callback: () => void) => void
        onError: (callback: (error: string) => void) => void
        onDownloadProgress: (callback: (progress: UpdateProgress) => void) => void
        onUpdateDownloaded: (callback: (info: UpdateInfo) => void) => void
      }
    }
  }
}

import { app, BrowserWindow, ipcMain, IpcMainInvokeEvent } from 'electron'
import { autoUpdater } from 'electron-updater'
import path from 'path'
import { isDev } from './utils'
import { DatabaseManager } from './database'

class App {
  private mainWindow: BrowserWindow | null = null
  private db: DatabaseManager

  constructor() {
    this.db = new DatabaseManager()
  }

  private createWindow(): void {
    this.mainWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      minWidth: 800,
      minHeight: 600,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        allowRunningInsecureContent: false,
        experimentalFeatures: false,
        preload: path.join(__dirname, 'preload.js'),
      },
      titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'default',
      show: false,
    })

    if (isDev) {
      this.mainWindow.loadURL('http://localhost:5173')
      this.mainWindow.webContents.openDevTools()
    } else {
      this.mainWindow.loadFile(path.join(__dirname, '../dist/index.html'))
    }

    this.mainWindow.once('ready-to-show', () => {
      this.mainWindow?.show()
    })

    this.mainWindow.on('closed', () => {
      this.mainWindow = null
    })
  }

  private validateSender(frame: any): boolean {
    // Validate the sender is from your app
    if (!frame || !frame.url) return false
    return frame.url.startsWith('file://') ||
           frame.url.startsWith('http://localhost:5173')
  }

  private validateInput(value: any, type: string, maxLength?: number): boolean {
    if (value === null || value === undefined) return false
    if (typeof value !== type) return false
    if (type === 'string' && maxLength && value.length > maxLength) return false
    return true
  }

  private setupIPC(): void {
    // Database operations with validation
    ipcMain.handle('db:getConversations', (event: IpcMainInvokeEvent) => {
      if (!this.validateSender(event.senderFrame)) return null
      return this.db.getConversations()
    })

    ipcMain.handle('db:getConversation', (event: IpcMainInvokeEvent, id: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(id, 'string', 100)) throw new Error('Invalid conversation ID')
      return this.db.getConversation(id)
    })

    ipcMain.handle('db:createConversation', (event: IpcMainInvokeEvent, title: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(title, 'string', 200)) throw new Error('Invalid conversation title')
      console.log('Main: Creating conversation with title:', title)
      const id = this.db.createConversation(title)
      console.log('Main: Created conversation:', id)
      return id
    })

    ipcMain.handle('db:updateConversation', (event: IpcMainInvokeEvent, id: string, title: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(id, 'string', 100)) throw new Error('Invalid conversation ID')
      if (!this.validateInput(title, 'string', 200)) throw new Error('Invalid conversation title')
      return this.db.updateConversation(id, title)
    })

    ipcMain.handle('db:deleteConversation', (event: IpcMainInvokeEvent, id: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(id, 'string', 100)) throw new Error('Invalid conversation ID')
      return this.db.deleteConversation(id)
    })

    ipcMain.handle('db:addMessage', (event: IpcMainInvokeEvent, conversationId: string, message: any) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(conversationId, 'string', 100)) throw new Error('Invalid conversation ID')
      if (!message || typeof message !== 'object') throw new Error('Invalid message object')
      return this.db.addMessage(conversationId, message)
    })

    ipcMain.handle('db:getMessages', (event: IpcMainInvokeEvent, conversationId: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(conversationId, 'string', 100)) throw new Error('Invalid conversation ID')
      return this.db.getMessages(conversationId)
    })

    ipcMain.handle('db:searchConversations', (event: IpcMainInvokeEvent, searchTerm: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(searchTerm, 'string', 200)) throw new Error('Invalid search term')
      return this.db.searchConversationsAndMessages(searchTerm)
    })

    // Settings with validation
    ipcMain.handle('settings:get', (event: IpcMainInvokeEvent, key: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(key, 'string', 50)) throw new Error('Invalid settings key')
      return this.db.getSetting(key)
    })

    ipcMain.handle('settings:set', (event: IpcMainInvokeEvent, key: string, value: any) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(key, 'string', 50)) throw new Error('Invalid settings key')
      this.db.setSetting(key, value)
      console.log('Settings updated:', key, value)
    })

    // Auto-updater IPC handlers
    ipcMain.handle('updater:check-for-updates', async (event: IpcMainInvokeEvent) => {
      if (!this.validateSender(event.senderFrame)) return null

      if (isDev) {
        return { available: false, message: 'Updates not available in development mode' }
      }

      try {
        const result = await autoUpdater.checkForUpdates()
        return { available: result ? result.updateInfo.version !== app.getVersion() : false }
      } catch (error: any) {
        console.error('Error checking for updates:', error)
        return { available: false, error: error.message }
      }
    })

    ipcMain.handle('updater:download-and-install', async (event: IpcMainInvokeEvent) => {
      if (!this.validateSender(event.senderFrame)) return null

      if (isDev) {
        return { success: false, message: 'Updates not available in development mode' }
      }

      try {
        await autoUpdater.downloadUpdate()
        autoUpdater.quitAndInstall()
        return { success: true }
      } catch (error: any) {
        console.error('Error downloading/installing update:', error)
        return { success: false, error: error.message }
      }
    })
  }

  private setupAutoUpdater(): void {
    if (!isDev) {
      autoUpdater.checkForUpdatesAndNotify()
    }

    // Auto-updater events
    autoUpdater.on('checking-for-update', () => {
      console.log('Checking for update...')
      if (this.mainWindow) {
        this.mainWindow.webContents.send('updater:checking-for-update')
      }
    })

    autoUpdater.on('update-available', (info) => {
      console.log('Update available:', info.version)
      if (this.mainWindow) {
        this.mainWindow.webContents.send('updater:update-available', info)
      }
    })

    autoUpdater.on('update-not-available', (info) => {
      console.log('Update not available')
      if (this.mainWindow) {
        this.mainWindow.webContents.send('updater:update-not-available')
      }
    })

    autoUpdater.on('error', (err) => {
      console.error('Auto-updater error:', err)
      if (this.mainWindow) {
        this.mainWindow.webContents.send('updater:error', err.message)
      }
    })

    autoUpdater.on('download-progress', (progressObj) => {
      console.log(`Download progress: ${progressObj.percent}%`)
      if (this.mainWindow) {
        this.mainWindow.webContents.send('updater:download-progress', progressObj)
      }
    })

    autoUpdater.on('update-downloaded', (info) => {
      console.log('Update downloaded:', info.version)
      if (this.mainWindow) {
        this.mainWindow.webContents.send('updater:update-downloaded', info)
      }
    })
  }

  public async init(): Promise<void> {
    await app.whenReady()

    this.setupIPC()
    this.setupAutoUpdater()
    this.createWindow()

    app.on('activate', () => {
      if (BrowserWindow.getAllWindows().length === 0) {
        this.createWindow()
      }
    })

    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        app.quit()
      }
    })
  }
}

const application = new App()
application.init().catch(console.error)

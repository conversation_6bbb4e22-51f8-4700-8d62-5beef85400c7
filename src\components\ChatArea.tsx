import React, { useEffect, useRef } from 'react'
import { useAppStore } from '../store'
import MessageBubble from './MessageBubble'
import StreamingMessageBubble from './StreamingMessageBubble'
import InputArea from './InputArea'
import { Bot } from './Icons'

const ChatArea: React.FC = () => {
  const {
    currentConversationId,
    messages,
    conversations,
    isLoading,
    streamingMessage,
    togglePinMessage
  } = useAppStore()
  
  const messagesEndRef = useRef<HTMLDivElement>(null)
  
  const currentConversation = conversations.find(c => c.id === currentConversationId)

  const scrollToBottom = () => {
    // Use requestAnimationFrame for better timing with DOM updates
    requestAnimationFrame(() => {
      setTimeout(() => {
        if (messagesEndRef.current) {
          messagesEndRef.current.scrollIntoView({
            behavior: 'smooth',
            block: 'end',
            inline: 'nearest'
          })
        }
      }, 50)
    })
  }

  const handleRegenerate = async (messageId: string) => {
    console.log('Regenerating message:', messageId)
    const { sendMessage, messages, currentConversationId } = useAppStore.getState()
    const messageToRegenerate = messages.find(m => m.id === messageId)
    console.log('Message to regenerate:', messageToRegenerate)
    
    if (messageToRegenerate && currentConversationId) {
      // Find the user message that preceded the assistant's message
      const precedingUserMessage = messages
        .slice(0, messages.findIndex(m => m.id === messageId))
        .reverse()
        .find(m => m.role === 'user')
      console.log('Preceding user message:', precedingUserMessage)

      if (precedingUserMessage) {
        await sendMessage(precedingUserMessage.content)
      } else {
        console.error('Could not find preceding user message to regenerate from.')
      }
    } else {
      console.error('Could not find message to regenerate or no current conversation.')
    }
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages, streamingMessage])

  // Additional effect to handle streaming message updates
  useEffect(() => {
    if (streamingMessage !== null) {
      scrollToBottom()
    }
  }, [streamingMessage])

  if (!currentConversationId) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <div className="h-16 w-16 rounded-full bg-indigo-500/10 flex items-center justify-center mx-auto mb-4">
            <Bot className="h-8 w-8 text-indigo-500" />
          </div>
          <h2 className="text-xl font-semibold mb-2">Welcome to ChatLocal</h2>
          <p className="text-neutral-400 mb-6 max-w-md">
            Start a new conversation to begin chatting with AI models through OpenRouter.
          </p>
          <button
            onClick={async () => {
              try {
                console.log('Starting new conversation from welcome screen...')
                const store = useAppStore.getState()
                const id = await store.createConversation('New Conversation')
                console.log('Created conversation:', id)
                store.setCurrentConversation(id)
                await store.loadMessages(id)
                console.log('Welcome screen conversation setup complete')
              } catch (error) {
                console.error('Failed to create conversation from welcome:', error)
                alert('Failed to create conversation: ' + (error instanceof Error ? error.message : 'Unknown error'))
              }
            }}
            className="btn-primary"
          >
            Start New Conversation
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 flex flex-col h-full">
      {/* Header */}
      <header className="flex-shrink-0 h-16 flex items-center gap-3 px-4 md:px-8 border-b border-neutral-800 bg-neutral-900/60 backdrop-blur-lg">
        <div className="h-8 w-8 rounded-full bg-indigo-500 flex items-center justify-center">
          <Bot className="h-4 w-4 text-white" />
        </div>
        <div>
          <h2 className="text-lg font-semibold tracking-tight leading-none">
            {currentConversation?.title || 'Conversation'}
          </h2>
          <p className="text-xs text-neutral-400">
            {messages.length} messages
          </p>
        </div>
      </header>

      {/* Messages */}
      <main className="flex-1 overflow-y-auto px-4 md:px-8 py-6 min-h-0">
        <div className="max-w-4xl mx-auto space-y-6">
          {messages.length === 0 ? (
            <div className="text-center py-12">
              <div className="h-12 w-12 rounded-full bg-neutral-800 flex items-center justify-center mx-auto mb-4">
                <Bot className="h-6 w-6 text-neutral-400" />
              </div>
              <p className="text-neutral-400">
                No messages yet. Start the conversation!
              </p>
            </div>
          ) : (
            messages.map((message) => (
              <MessageBubble 
                key={message.id} 
                message={message} 
                onRegenerate={handleRegenerate}
                onPinMessage={togglePinMessage}
              />
            ))
          )}

          {/* Streaming message */}
          {streamingMessage !== null && (
            <StreamingMessageBubble content={streamingMessage} />
          )}

          {/* Loading indicator (when not streaming) */}
          {isLoading && streamingMessage === null && (
            <div className="flex items-start gap-3">
              <div className="h-8 w-8 rounded-full bg-indigo-500 flex items-center justify-center">
                <Bot className="h-4 w-4 text-white" />
              </div>
              <div className="max-w-md">
                <div className="bg-neutral-800 rounded-lg px-4 py-2">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-neutral-500 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-neutral-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-2 h-2 bg-neutral-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                </div>
              </div>
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </div>
      </main>

      {/* Input Area */}
      <div className="flex-shrink-0">
        <InputArea />
      </div>
    </div>
  )
}

export default ChatArea

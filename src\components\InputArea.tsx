import React, { useState, useRef, useEffect } from 'react'
import { useAppStore } from '../store'
import { Send, Plus, Settings, Loader2 } from './Icons'
import ChatSettingsDrawer from './ChatSettingsDrawer'

const InputArea: React.FC = () => {
  const { currentConversationId, sendMessage, isLoading, models, settings } = useAppStore()
  const [input, setInput] = useState('')
  const [showSettings, setShowSettings] = useState(false)
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!input.trim() || !currentConversationId || isLoading) return

    const message = input.trim()
    setInput('')

    // Reset textarea height
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto'
    }

    try {
      await sendMessage(message, currentConversationId)
    } catch (error) {
      console.error('Error sending message:', error)
      // The error will be handled by the store and displayed in the chat
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSubmit(e)
    }
  }

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto'
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`
    }
  }, [input])

  if (!currentConversationId) {
    return null
  }

  return (
    <div className="border-t border-neutral-800 bg-neutral-900/60 backdrop-blur-lg">
      <form onSubmit={handleSubmit} className="p-4 md:p-6">
        <div className="max-w-4xl mx-auto flex items-end gap-3">
          {/* Attachment button */}
          <button
            type="button"
            className="h-10 w-10 flex items-center justify-center rounded-lg hover:bg-neutral-800 transition-colors shrink-0"
            title="Attach file"
          >
            <Plus className="h-5 w-5" />
          </button>

          {/* Input field */}
          <div className="flex-1 relative">
            <textarea
              ref={textareaRef}
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Type your message… (Shift+Enter for new line)"
              className="w-full bg-neutral-900 border border-neutral-800 rounded-lg px-4 py-3 pr-12 text-sm placeholder-neutral-500 focus:ring-2 focus:ring-indigo-500 focus:border-transparent outline-none resize-none min-h-[44px] max-h-32"
              rows={1}
              disabled={isLoading}
            />
            
            {/* Character count */}
            {input.length > 0 && (
              <div className="absolute bottom-1 right-12 text-xs text-neutral-500">
                {input.length}
              </div>
            )}
          </div>

          {/* Settings button */}
          <button
            type="button"
            onClick={() => setShowSettings(true)}
            className="h-10 w-10 flex items-center justify-center rounded-lg transition-colors shrink-0 bg-neutral-800 hover:bg-neutral-700 text-neutral-400 hover:text-neutral-300"
            title="Chat settings"
          >
            <Settings className="h-4 w-4" />
          </button>

          {/* Send button */}
          <button
            type="submit"
            disabled={!input.trim() || isLoading}
            className={`
              h-10 w-10 flex items-center justify-center rounded-lg transition-colors shrink-0
              ${input.trim() && !isLoading
                ? 'bg-indigo-500 hover:bg-indigo-600 text-white'
                : 'bg-neutral-800 text-neutral-500 cursor-not-allowed'
              }
            `}
            title="Send message"
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Send className="h-4 w-4" />
            )}
          </button>
        </div>

        {/* Model selector and options */}
        <div className="max-w-4xl mx-auto mt-2 flex items-center justify-between text-xs text-neutral-500">
          <div className="flex items-center gap-4">
            <span>Model: {settings.selectedModel ?
              models.find(m => m.id === settings.selectedModel)?.name || settings.selectedModel
              : 'No model selected'}</span>
            <span>•</span>
            <span>Temp: {settings.temperature?.toFixed(1) || '0.7'}</span>
            <span>•</span>
            <span>Max: {settings.maxTokens?.toLocaleString() || '4K'}</span>
            {settings.topP && (
              <>
                <span>•</span>
                <span>Top-P: {settings.topP.toFixed(2)}</span>
              </>
            )}
          </div>

          <div className="flex items-center gap-2">
            <span>Shift+Enter for new line</span>
          </div>
        </div>
      </form>

      {/* Chat Settings Drawer */}
      <ChatSettingsDrawer
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
      />
    </div>
  )
}

export default InputArea

# ChatLo Technical Architecture

## System Overview

ChatLo is a desktop AI chat application built with a modern Electron + React stack, designed for local privacy while accessing cloud AI models through OpenRouter.

## Architecture Diagram

```
┌─────────────────────────────────────────────────────────────┐
│                    ChatLo Desktop App                       │
├─────────────────────────────────────────────────────────────┤
│  Renderer Process (React + TypeScript)                     │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │   Components    │  │   State Store   │  │   Services  │ │
│  │   - Sidebar     │  │   (Zustand)     │  │   - OpenRouter│ │
│  │   - Chat<PERSON><PERSON>    │  │   - Messages    │  │   - Database │ │
│  │   - Settings    │  │   - Settings    │  │   - Updates  │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  Main Process (Node.js)                                    │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │   IPC Handlers  │  │   Database      │  │   Updater   │ │
│  │   - DB Ops      │  │   (SQLite)      │  │   (electron-│ │
│  │   - Settings    │  │   - Migrations  │  │    updater) │ │
│  │   - Updates     │  │   - CRUD Ops    │  │             │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │   External APIs │
                    │   - OpenRouter  │
                    │   - GitHub      │
                    │     (Updates)   │
                    └─────────────────┘
```

## Core Components

### 1. Main Process (main.js)
**Responsibilities:**
- Window management and lifecycle
- Database initialization and management
- IPC communication with renderer
- Auto-updater configuration
- System-level operations

**Key Features:**
- SQLite database with migration system
- Secure IPC handlers for all database operations
- Auto-updater with GitHub releases
- Development vs production environment handling

### 2. Renderer Process (React App)
**Responsibilities:**
- User interface rendering
- User interaction handling
- State management
- API communication

**Architecture Pattern:** Component-Service-Store

### 3. Database Layer (database.js)
**Design Pattern:** Repository Pattern with Migration System

**Schema:**
```sql
-- Conversations table
CREATE TABLE conversations (
  id TEXT PRIMARY KEY,
  title TEXT NOT NULL,
  created_at TEXT NOT NULL,
  updated_at TEXT NOT NULL
);

-- Messages table  
CREATE TABLE messages (
  id TEXT PRIMARY KEY,
  conversation_id TEXT NOT NULL,
  role TEXT NOT NULL,
  content TEXT NOT NULL,
  model TEXT,
  created_at TEXT NOT NULL,
  FOREIGN KEY (conversation_id) REFERENCES conversations (id)
);

-- Settings table
CREATE TABLE settings (
  key TEXT PRIMARY KEY,
  value TEXT NOT NULL -- JSON serialized
);
```

**Features:**
- WAL mode for better performance
- Foreign key constraints
- Automatic ID generation
- JSON settings storage
- Migration versioning system

### 4. State Management (Zustand)
**Store Structure:**
```typescript
interface AppState {
  // Data
  conversations: Conversation[]
  messages: Message[]
  settings: Settings
  
  // UI State
  sidebarOpen: boolean
  isLoading: boolean
  streamingMessage: string | null
  
  // Actions
  loadConversations: () => Promise<void>
  sendStreamingMessage: (content: string) => Promise<void>
  // ... other actions
}
```

**Design Principles:**
- Async actions for all database operations
- Optimistic UI updates where appropriate
- Error handling in every async action
- Separation of sync and async state updates

### 5. OpenRouter Service
**Architecture:** Service Layer Pattern

**Features:**
- API key management and validation
- Streaming and non-streaming completions
- Error handling and retry logic
- Model parameter configuration
- Request/response transformation

**Implementation:**
```typescript
class OpenRouterService {
  private apiKey: string
  private baseURL: string
  
  async createStreamingChatCompletion(
    params: ChatCompletionParams,
    onChunk: (chunk: string) => void,
    onComplete: () => void,
    onError: (error: Error) => void
  ): Promise<void>
}
```

## Data Flow

### 1. Message Sending Flow
```
User Input → InputArea → Store.sendStreamingMessage() → 
OpenRouter API → Streaming Response → UI Update → 
Database Save → State Refresh
```

### 2. Conversation Management Flow
```
User Action → Component → Store Action → IPC Call → 
Database Operation → IPC Response → Store Update → UI Refresh
```

### 3. Settings Flow
```
Settings Change → Component → Store.updateSettings() → 
IPC Call → Database Save → Local State Update
```

## Security Architecture

### 1. Process Isolation
- Main process handles all sensitive operations
- Renderer process has no direct file system access
- IPC communication for all data operations

### 2. API Key Security
- API keys stored in encrypted database
- Never exposed to renderer process logs
- Validated before use

### 3. Content Security Policy
- Strict CSP in renderer process
- No eval() or inline scripts
- Controlled external resource loading

## Performance Optimizations

### 1. Database
- WAL mode for concurrent reads
- Indexed queries for fast lookups
- Prepared statements for repeated operations
- Connection pooling and reuse

### 2. UI Rendering
- React 19 concurrent features
- Virtualized conversation lists (planned)
- Debounced search and filtering
- Lazy loading of message history

### 3. Memory Management
- Streaming message handling
- Cleanup of event listeners
- Proper component unmounting
- Database connection management

## Error Handling Strategy

### 1. Layered Error Handling
```
UI Component → Store Action → Service Layer → 
Database/API → Error Boundary → User Notification
```

### 2. Error Types
- **Network Errors**: API timeouts, connection issues
- **Database Errors**: Schema violations, disk space
- **Validation Errors**: Invalid input, missing data
- **System Errors**: File permissions, memory issues

### 3. User Experience
- Toast notifications for errors
- Graceful degradation
- Retry mechanisms
- Clear error messages

## Deployment Architecture

### 1. Build Process
```
TypeScript Compilation → Vite Build → 
Electron Packaging → Code Signing → 
Distribution (GitHub Releases)
```

### 2. Update Mechanism
- GitHub releases for update distribution
- Differential updates for efficiency
- Automatic background checking
- User-controlled installation

### 3. Platform Support
- Windows (NSIS installer)
- macOS (DMG package)
- Linux (AppImage)

## Development Architecture

### 1. Development Stack
- **Frontend**: React 19 + TypeScript + Tailwind CSS
- **Backend**: Node.js + Electron + SQLite
- **Build**: Vite + Electron Builder
- **State**: Zustand + React Query (planned)

### 2. Code Organization
```
src/
├── components/     # React UI components
├── services/       # Business logic services
├── store/          # State management
├── types/          # TypeScript definitions
├── utils/          # Helper functions
└── hooks/          # Custom React hooks (planned)
```

### 3. Development Workflow
- Hot reload in development
- TypeScript strict mode
- ESLint + Prettier
- Concurrent dev server and Electron

## Scalability Considerations

### 1. Data Growth
- Conversation pagination
- Message archiving
- Database vacuum operations
- Storage quota management

### 2. Feature Expansion
- Plugin architecture (MCP integration)
- Modular component system
- Service abstraction layers
- Configuration-driven features

### 3. Performance Scaling
- Virtual scrolling for large lists
- Background processing
- Caching strategies
- Resource optimization

import React, { useState, useEffect } from 'react'
import { useAppStore } from '../store'
import { ArrowLeft, Key, Settings, User, FileText, Download } from '../components/Icons'
import { useNavigate } from 'react-router-dom'

const SettingsPage: React.FC = () => {
  const navigate = useNavigate()
  const { settings, updateSettings } = useAppStore()
  const [activeTab, setActiveTab] = useState('api')
  const [localSettings, setLocalSettings] = useState(settings)
  const [isLoading, setIsLoading] = useState(false)
  const [testResult, setTestResult] = useState<string | null>(null)
  const [chatloPath, setChatloPath] = useState('')
  const [storageInfo, setStorageInfo] = useState<any>(null)

  useEffect(() => {
    setLocalSettings(settings)
    loadChatloPath()
    loadStorageInfo()
  }, [settings])

  const loadChatloPath = async () => {
    try {
      if (window.electronAPI?.files) {
        const path = await window.electronAPI.files.getChatloFolderPath()
        setChatloPath(path)
      }
    } catch (error) {
      console.error('Error loading Chatlo path:', error)
    }
  }

  const loadStorageInfo = async () => {
    try {
      if (window.electronAPI?.files) {
        const files = await window.electronAPI.files.getIndexedFiles()
        const totalSize = files.reduce((sum, file) => sum + file.file_size, 0)
        setStorageInfo({
          totalFiles: files.length,
          totalSize: totalSize,
          fileTypes: files.reduce((acc, file) => {
            acc[file.file_type] = (acc[file.file_type] || 0) + 1
            return acc
          }, {} as Record<string, number>)
        })
      }
    } catch (error) {
      console.error('Error loading storage info:', error)
    }
  }

  const handleSave = async () => {
    try {
      setIsLoading(true)
      updateSettings(localSettings)

      if (window.electronAPI?.settings) {
        await window.electronAPI.settings.set('app-settings', localSettings)
      }

      setTestResult('✅ Settings saved successfully!')
      setTimeout(() => setTestResult(null), 3000)
    } catch (error) {
      console.error('Error saving settings:', error)
      setTestResult('❌ Failed to save settings')
      setTimeout(() => setTestResult(null), 3000)
    } finally {
      setIsLoading(false)
    }
  }

  const testApiKey = async () => {
    if (!localSettings.openRouterApiKey) {
      setTestResult('❌ Please enter an API key first')
      return
    }

    try {
      setIsLoading(true)
      setTestResult('Testing API key...')

      // Import the service dynamically to avoid circular dependencies
      const { openRouterService } = await import('../services/openrouter')
      openRouterService.setApiKey(localSettings.openRouterApiKey)

      const validation = await openRouterService.validateApiKey()

      if (validation.valid) {
        setTestResult('✅ API key is valid!')
      } else {
        setTestResult('❌ Invalid API key')
      }
    } catch (error) {
      console.error('Error testing API key:', error)
      setTestResult('❌ Failed to test API key')
    } finally {
      setIsLoading(false)
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
  }

  const tabs = [
    { id: 'api', label: 'API Setup', icon: Key },
    { id: 'data', label: 'Data Management', icon: FileText },
    { id: 'profile', label: 'User Profile', icon: User },
  ]

  return (
    <div className="h-screen flex flex-col bg-neutral-950 text-neutral-100">
      {/* Header */}
      <header className="flex items-center gap-4 h-16 px-6 border-b border-neutral-800 bg-neutral-900/60 backdrop-blur-lg">
        <button
          onClick={() => navigate('/')}
          className="h-8 w-8 flex items-center justify-center rounded-lg hover:bg-neutral-800 transition-colors"
        >
          <ArrowLeft className="h-4 w-4" />
        </button>
        <div className="flex items-center gap-2">
          <Settings className="h-5 w-5 text-indigo-400" />
          <h1 className="text-xl font-semibold">Settings</h1>
        </div>
      </header>

      <div className="flex-1 flex">
        {/* Sidebar */}
        <nav className="w-64 border-r border-neutral-800 bg-neutral-900/30">
          <div className="p-4">
            <div className="space-y-1">
              {tabs.map((tab) => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`
                      w-full flex items-center gap-3 px-3 py-2 rounded-lg text-sm transition-colors
                      ${activeTab === tab.id
                        ? 'bg-indigo-500 text-white'
                        : 'text-neutral-400 hover:text-neutral-200 hover:bg-neutral-800'
                      }
                    `}
                  >
                    <Icon className="h-4 w-4" />
                    {tab.label}
                  </button>
                )
              })}
            </div>
          </div>
        </nav>

        {/* Main Content */}
        <main className="flex-1 overflow-y-auto">
          <div className="max-w-4xl mx-auto p-8">
            {activeTab === 'api' && (
              <div className="space-y-6">
                <div>
                  <h2 className="text-2xl font-semibold mb-2">API Configuration</h2>
                  <p className="text-neutral-400">Configure your OpenRouter API key to access AI models.</p>
                </div>

                <div className="bg-neutral-900/50 border border-neutral-800 rounded-lg p-6">
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium mb-2">
                        OpenRouter API Key
                      </label>
                      <div className="flex gap-3">
                        <input
                          type="password"
                          value={localSettings.openRouterApiKey || ''}
                          onChange={(e) => setLocalSettings(prev => ({
                            ...prev,
                            openRouterApiKey: e.target.value
                          }))}
                          placeholder="sk-or-..."
                          className="flex-1 bg-neutral-800 border border-neutral-700 rounded-lg px-4 py-2 text-sm focus:ring-2 focus:ring-indigo-500 focus:border-transparent outline-none"
                        />
                        <button
                          onClick={testApiKey}
                          disabled={isLoading}
                          className="px-4 py-2 bg-neutral-700 hover:bg-neutral-600 text-white rounded-lg text-sm font-medium transition-colors disabled:opacity-50"
                        >
                          Test
                        </button>
                      </div>
                      {testResult && (
                        <p className="mt-2 text-sm">{testResult}</p>
                      )}
                    </div>

                    <div className="flex gap-3">
                      <button
                        onClick={handleSave}
                        disabled={isLoading}
                        className="px-6 py-2 bg-indigo-500 hover:bg-indigo-600 text-white rounded-lg text-sm font-medium transition-colors disabled:opacity-50"
                      >
                        Save Settings
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'data' && (
              <div className="space-y-6">
                <div>
                  <h2 className="text-2xl font-semibold mb-2">Data Management</h2>
                  <p className="text-neutral-400">Manage your files, conversations, and storage.</p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* File Storage */}
                  <div className="bg-neutral-900/50 border border-neutral-800 rounded-lg p-6">
                    <h3 className="text-lg font-medium mb-4">File Storage</h3>
                    <div className="space-y-3">
                      <div className="flex justify-between text-sm">
                        <span className="text-neutral-400">Chatlo Folder:</span>
                        <span className="text-neutral-200 font-mono text-xs">{chatloPath}</span>
                      </div>
                      {storageInfo && (
                        <>
                          <div className="flex justify-between text-sm">
                            <span className="text-neutral-400">Total Files:</span>
                            <span className="text-neutral-200">{storageInfo.totalFiles}</span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span className="text-neutral-400">Total Size:</span>
                            <span className="text-neutral-200">{formatFileSize(storageInfo.totalSize)}</span>
                          </div>
                        </>
                      )}
                    </div>
                  </div>

                  {/* File Types */}
                  {storageInfo && (
                    <div className="bg-neutral-900/50 border border-neutral-800 rounded-lg p-6">
                      <h3 className="text-lg font-medium mb-4">File Types</h3>
                      <div className="space-y-2">
                        {Object.entries(storageInfo.fileTypes).map(([type, count]) => (
                          <div key={type} className="flex justify-between text-sm">
                            <span className="text-neutral-400 capitalize">{type}:</span>
                            <span className="text-neutral-200">{count}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {activeTab === 'profile' && (
              <div className="space-y-6">
                <div>
                  <h2 className="text-2xl font-semibold mb-2">User Profile</h2>
                  <p className="text-neutral-400">Manage your profile and preferences.</p>
                </div>

                <div className="bg-neutral-900/50 border border-neutral-800 rounded-lg p-6">
                  <div className="text-center py-8">
                    <User className="h-12 w-12 text-neutral-500 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-neutral-400 mb-2">Coming Soon</h3>
                    <p className="text-sm text-neutral-500">
                      User profile features will be available in a future update.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </main>
      </div>
    </div>
  )
}

export default SettingsPage

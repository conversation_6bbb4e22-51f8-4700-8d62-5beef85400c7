import * as fs from 'fs'
import * as path from 'path'
import * as XLSX from 'xlsx'
import * as mime from 'mime-types'

// Lazy require for optional dependencies
let pdfParse: any
let mammoth: any
let sharp: any

// Initialize modules with lazy loading
const initModules = () => {
  if (!pdfParse) {
    try {
      pdfParse = require('pdf-parse')
    } catch (error) {
      console.warn('pdf-parse not available:', error)
      pdfParse = null
    }
  }
  if (!mammoth) {
    try {
      mammoth = require('mammoth')
    } catch (error) {
      console.warn('mammoth not available:', error)
      mammoth = null
    }
  }
  if (!sharp) {
    try {
      sharp = require('sharp')
    } catch (error) {
      console.warn('sharp not available:', error)
      sharp = null
    }
  }
}

export interface ProcessedFileContent {
  text?: string
  metadata?: any
  error?: string
}

export class FileProcessorService {
  
  // Process PDF files
  async processPDF(filePath: string): Promise<ProcessedFileContent> {
    try {
      initModules()

      if (!pdfParse) {
        return {
          error: 'PDF processing not available - pdf-parse module not loaded'
        }
      }

      const dataBuffer = fs.readFileSync(filePath)
      const data = await pdfParse(dataBuffer)

      return {
        text: data.text,
        metadata: {
          pages: data.numpages,
          info: data.info,
          version: data.version
        }
      }
    } catch (error: any) {
      console.error('Error processing PDF:', error)
      return {
        error: `Failed to process PDF: ${error?.message || 'Unknown error'}`
      }
    }
  }

  // Process Word documents (.docx)
  async processWord(filePath: string): Promise<ProcessedFileContent> {
    try {
      initModules()

      if (!mammoth) {
        return {
          error: 'Word processing not available - mammoth module not loaded'
        }
      }

      const result = await mammoth.extractRawText({ path: filePath })

      return {
        text: result.value,
        metadata: {
          messages: result.messages,
          hasImages: result.messages.some((msg: any) => msg.type === 'image')
        }
      }
    } catch (error: any) {
      console.error('Error processing Word document:', error)
      return {
        error: `Failed to process Word document: ${error?.message || 'Unknown error'}`
      }
    }
  }

  // Process Excel files (.xlsx, .xls)
  async processExcel(filePath: string): Promise<ProcessedFileContent> {
    try {
      const workbook = XLSX.readFile(filePath)
      const sheetNames = workbook.SheetNames
      let allText = ''
      const sheetsData: any = {}

      for (const sheetName of sheetNames) {
        const worksheet = workbook.Sheets[sheetName]
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })
        
        // Convert to text
        const sheetText = jsonData
          .map((row: any) => (row as any[]).join('\t'))
          .join('\n')
        
        allText += `Sheet: ${sheetName}\n${sheetText}\n\n`
        sheetsData[sheetName] = jsonData
      }

      return {
        text: allText.trim(),
        metadata: {
          sheets: sheetNames,
          sheetsData: sheetsData,
          totalSheets: sheetNames.length
        }
      }
    } catch (error: any) {
      console.error('Error processing Excel file:', error)
      return {
        error: `Failed to process Excel file: ${error?.message || 'Unknown error'}`
      }
    }
  }

  // Process PowerPoint files (.pptx)
  async processPowerPoint(filePath: string): Promise<ProcessedFileContent> {
    try {
      // For PowerPoint, we'll use a simpler approach since there's no direct library
      // This is a placeholder - in a real implementation, you might use a library like 'officegen' or similar
      const stats = fs.statSync(filePath)
      
      return {
        text: `PowerPoint presentation: ${path.basename(filePath)}\nFile size: ${stats.size} bytes\nNote: PowerPoint content extraction requires additional processing.`,
        metadata: {
          fileSize: stats.size,
          lastModified: stats.mtime,
          note: 'PowerPoint content extraction not fully implemented'
        }
      }
    } catch (error: any) {
      console.error('Error processing PowerPoint file:', error)
      return {
        error: `Failed to process PowerPoint file: ${error?.message || 'Unknown error'}`
      }
    }
  }

  // Process text files
  async processText(filePath: string): Promise<ProcessedFileContent> {
    try {
      const content = fs.readFileSync(filePath, 'utf8')
      const stats = fs.statSync(filePath)
      
      return {
        text: content,
        metadata: {
          encoding: 'utf8',
          lines: content.split('\n').length,
          characters: content.length,
          fileSize: stats.size,
          lastModified: stats.mtime
        }
      }
    } catch (error: any) {
      console.error('Error processing text file:', error)
      return {
        error: `Failed to process text file: ${error?.message || 'Unknown error'}`
      }
    }
  }

  // Process markdown files
  async processMarkdown(filePath: string): Promise<ProcessedFileContent> {
    try {
      const content = fs.readFileSync(filePath, 'utf8')
      const stats = fs.statSync(filePath)
      
      // Extract headers for metadata
      const headers = content.match(/^#+\s+(.+)$/gm) || []
      
      return {
        text: content,
        metadata: {
          encoding: 'utf8',
          lines: content.split('\n').length,
          characters: content.length,
          headers: headers.map(h => h.replace(/^#+\s+/, '')),
          fileSize: stats.size,
          lastModified: stats.mtime
        }
      }
    } catch (error: any) {
      console.error('Error processing markdown file:', error)
      return {
        error: `Failed to process markdown file: ${error?.message || 'Unknown error'}`
      }
    }
  }

  // Process image files
  async processImage(filePath: string): Promise<ProcessedFileContent> {
    try {
      const stats = fs.statSync(filePath)
      const mimeType = mime.lookup(filePath) || 'unknown'
      
      // Get image metadata using sharp
      const metadata = await sharp(filePath).metadata()
      
      return {
        text: `Image: ${path.basename(filePath)}\nDimensions: ${metadata.width}x${metadata.height}\nFormat: ${metadata.format}\nFile size: ${stats.size} bytes`,
        metadata: {
          width: metadata.width,
          height: metadata.height,
          format: metadata.format,
          channels: metadata.channels,
          density: metadata.density,
          hasAlpha: metadata.hasAlpha,
          mimeType: mimeType,
          fileSize: stats.size,
          lastModified: stats.mtime
        }
      }
    } catch (error: any) {
      console.error('Error processing image file:', error)
      return {
        error: `Failed to process image file: ${error?.message || 'Unknown error'}`
      }
    }
  }

  // Main processing method that routes to appropriate processor
  async processFile(filePath: string, fileType: string): Promise<ProcessedFileContent> {
    try {
      switch (fileType) {
        case 'pdf':
          return await this.processPDF(filePath)
        case 'word':
          return await this.processWord(filePath)
        case 'excel':
          return await this.processExcel(filePath)
        case 'powerpoint':
          return await this.processPowerPoint(filePath)
        case 'text':
          return await this.processText(filePath)
        case 'markdown':
          return await this.processMarkdown(filePath)
        case 'image':
          return await this.processImage(filePath)
        default:
          return {
            error: `Unsupported file type: ${fileType}`
          }
      }
    } catch (error: any) {
      console.error('Error in processFile:', error)
      return {
        error: `Failed to process file: ${error?.message || 'Unknown error'}`
      }
    }
  }

  // Batch process multiple files
  async processFiles(files: Array<{ filePath: string, fileType: string }>): Promise<Array<ProcessedFileContent & { filePath: string }>> {
    const results = []
    
    for (const file of files) {
      const result = await this.processFile(file.filePath, file.fileType)
      results.push({
        ...result,
        filePath: file.filePath
      })
    }
    
    return results
  }

  // Check if file type is supported
  isFileTypeSupported(fileType: string): boolean {
    const supportedTypes = ['pdf', 'word', 'excel', 'powerpoint', 'text', 'markdown', 'image']
    return supportedTypes.includes(fileType)
  }

  // Get supported file extensions
  getSupportedExtensions(): string[] {
    return [
      '.pdf',
      '.doc', '.docx',
      '.xls', '.xlsx',
      '.ppt', '.pptx',
      '.txt',
      '.md',
      '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'
    ]
  }
}

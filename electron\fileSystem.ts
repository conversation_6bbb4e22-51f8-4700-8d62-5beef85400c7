import { app } from 'electron'
import * as fs from 'fs'
import * as path from 'path'
import * as crypto from 'crypto'
import { DatabaseManager, FileRecord } from './database'
import { FileProcessorService } from './fileProcessors'
import { isDev } from './utils'

export class FileSystemManager {
  private dbManager: DatabaseManager
  private chatloFolderPath: string
  private fileProcessor: FileProcessorService

  constructor(dbManager: DatabaseManager) {
    this.dbManager = dbManager
    this.chatloFolderPath = path.join(app.getPath('documents'), 'Chatlo')
    this.fileProcessor = new FileProcessorService()
  }

  // Initialize the default Chatlo folder structure
  async initializeChatloFolder(): Promise<void> {
    try {
      // Create main Chatlo folder
      if (!fs.existsSync(this.chatloFolderPath)) {
        fs.mkdirSync(this.chatloFolderPath, { recursive: true })
        console.log('Created Chatlo folder at:', this.chatloFolderPath)
      }

      // Create subfolders
      const subfolders = ['Documents', 'Images', 'Uploads', 'Exports']
      for (const subfolder of subfolders) {
        const subfolderPath = path.join(this.chatloFolderPath, subfolder)
        if (!fs.existsSync(subfolderPath)) {
          fs.mkdirSync(subfolderPath, { recursive: true })
        }
      }

      // Create a welcome file
      const welcomeFile = path.join(this.chatloFolderPath, 'Welcome.md')
      if (!fs.existsSync(welcomeFile)) {
        const welcomeContent = `# Welcome to ChatLo File Storage

This folder is where ChatLo stores and indexes your files for AI interactions.

## Folder Structure
- **Documents/**: Store your text documents, PDFs, Word files here
- **Images/**: Store images for AI vision analysis
- **Uploads/**: Files uploaded through the chat interface
- **Exports/**: Exported conversations and data

## How to Use
1. Place files in the appropriate folders
2. Use @filename in chat to reference files
3. Use the + button to attach files to messages
4. ChatLo will automatically index and process your files

Happy chatting! 🚀
`
        fs.writeFileSync(welcomeFile, welcomeContent, 'utf8')
      }

      console.log('Chatlo folder structure initialized')
    } catch (error) {
      console.error('Error initializing Chatlo folder:', error)
      throw error
    }
  }

  // Get the Chatlo folder path
  getChatloFolderPath(): string {
    return this.chatloFolderPath
  }

  // Set a new Chatlo folder path
  async setChatloFolderPath(newPath: string): Promise<void> {
    try {
      // Validate the path exists
      if (!fs.existsSync(newPath)) {
        throw new Error('Selected folder does not exist')
      }

      // Update the path
      this.chatloFolderPath = newPath

      // Initialize the new folder structure
      await this.initializeChatloFolder()

      // Re-index all files in the new location
      await this.indexAllFiles()

      console.log('Chatlo folder path updated to:', newPath)
    } catch (error) {
      console.error('Error setting Chatlo folder path:', error)
      throw error
    }
  }

  // Calculate file hash for deduplication
  private calculateFileHash(filePath: string): string {
    const fileBuffer = fs.readFileSync(filePath)
    return crypto.createHash('sha256').update(fileBuffer).digest('hex')
  }

  // Get file type from extension
  private getFileType(filename: string): string {
    const ext = path.extname(filename).toLowerCase()
    const typeMap: { [key: string]: string } = {
      '.pdf': 'pdf',
      '.doc': 'word',
      '.docx': 'word',
      '.xls': 'excel',
      '.xlsx': 'excel',
      '.ppt': 'powerpoint',
      '.pptx': 'powerpoint',
      '.txt': 'text',
      '.md': 'markdown',
      '.jpg': 'image',
      '.jpeg': 'image',
      '.png': 'image',
      '.gif': 'image',
      '.bmp': 'image',
      '.webp': 'image',
      '.svg': 'image'
    }
    return typeMap[ext] || 'unknown'
  }

  // Index a single file (metadata only by default, lazy content processing)
  async indexFile(filePath: string, processContent: boolean = false): Promise<string | null> {
    try {
      if (!fs.existsSync(filePath)) {
        console.warn('File does not exist:', filePath)
        return null
      }

      const stats = fs.statSync(filePath)
      const filename = path.basename(filePath)
      const fileType = this.getFileType(filename)
      const contentHash = this.calculateFileHash(filePath)

      // Check if file already exists in database
      const existingFile = this.dbManager.getFileByPath(filePath)
      if (existingFile && existingFile.content_hash === contentHash) {
        console.log('File already indexed and unchanged:', filename)
        return existingFile.id
      }

      // Add or update file in database
      const fileRecord: Omit<FileRecord, 'id' | 'created_at' | 'updated_at'> = {
        filename,
        filepath: filePath,
        file_type: fileType,
        file_size: stats.size,
        content_hash: contentHash,
        extracted_content: undefined, // Will be populated by file processors
        metadata: JSON.stringify({
          lastModified: stats.mtime.toISOString(),
          isDirectory: stats.isDirectory(),
          extension: path.extname(filename)
        })
      }

      let fileId: string
      if (existingFile) {
        this.dbManager.updateFile(existingFile.id, fileRecord)
        console.log('Updated file index:', filename)
        fileId = existingFile.id
      } else {
        fileId = this.dbManager.addFile(fileRecord)
        console.log('Indexed new file:', filename)
      }

      // Only process file content if explicitly requested (for chat usage)
      if (processContent && this.fileProcessor.isFileTypeSupported(fileType)) {
        try {
          console.log('Processing file content for:', filename)
          const processedContent = await this.fileProcessor.processFile(filePath, fileType)

          if (processedContent.text && !processedContent.error) {
            // Update the file record with extracted content
            this.dbManager.updateFile(fileId, {
              extracted_content: processedContent.text,
              metadata: JSON.stringify({
                ...JSON.parse(fileRecord.metadata || '{}'),
                processed: true,
                processingMetadata: processedContent.metadata
              })
            })
            console.log('File content processed successfully:', filename)
          } else if (processedContent.error) {
            console.warn('File processing failed:', filename, processedContent.error)
          }
        } catch (error) {
          console.error('Error processing file content:', filename, error)
        }
      } else if (!processContent) {
        console.log('File indexed (metadata only):', filename)
      }

      return fileId
    } catch (error) {
      console.error('Error indexing file:', filePath, error)
      return null
    }
  }

  // Index all files in the Chatlo folder
  async indexAllFiles(): Promise<void> {
    console.log('Starting file indexing...')
    try {
      await this.indexDirectory(this.chatloFolderPath)
      console.log('File indexing completed')
    } catch (error) {
      console.error('Error during file indexing:', error)
    }
  }

  // Recursively index files in a directory
  private async indexDirectory(dirPath: string): Promise<void> {
    const items = fs.readdirSync(dirPath)
    
    for (const item of items) {
      const itemPath = path.join(dirPath, item)
      const stats = fs.statSync(itemPath)
      
      if (stats.isDirectory()) {
        // Skip hidden directories and node_modules
        if (!item.startsWith('.') && item !== 'node_modules') {
          await this.indexDirectory(itemPath)
        }
      } else if (stats.isFile()) {
        // Skip hidden files and system files
        if (!item.startsWith('.') && !item.startsWith('~')) {
          await this.indexFile(itemPath)
        }
      }
    }
  }

  // Get all indexed files
  getIndexedFiles(): FileRecord[] {
    return this.dbManager.getFiles()
  }

  // Search files by name or content with limit
  searchFiles(query: string, limit: number = 10): FileRecord[] {
    return this.dbManager.searchFiles(query, limit)
  }

  // Process file content on-demand (for chat usage)
  async processFileContent(fileId: string): Promise<boolean> {
    try {
      const file = this.dbManager.getFile(fileId)
      if (!file) {
        console.error('File not found:', fileId)
        return false
      }

      // Skip if already processed
      if (file.extracted_content) {
        console.log('File content already processed:', file.filename)
        return true
      }

      // Check if file still exists
      if (!fs.existsSync(file.filepath)) {
        console.warn('File no longer exists:', file.filepath)
        return false
      }

      const fileType = this.getFileType(file.filename)
      if (!this.fileProcessor.isFileTypeSupported(fileType)) {
        console.log('File type not supported for content processing:', fileType)
        return false
      }

      console.log('Processing file content on-demand:', file.filename)
      const processedContent = await this.fileProcessor.processFile(file.filepath, fileType)

      if (processedContent.text && !processedContent.error) {
        // Update the file record with extracted content
        this.dbManager.updateFile(fileId, {
          extracted_content: processedContent.text,
          metadata: JSON.stringify({
            ...JSON.parse(file.metadata || '{}'),
            processed: true,
            processingMetadata: processedContent.metadata
          })
        })
        console.log('File content processed successfully:', file.filename)
        return true
      } else if (processedContent.error) {
        console.warn('File processing failed:', file.filename, processedContent.error)
        return false
      }

      return false
    } catch (error) {
      console.error('Error processing file content on-demand:', error)
      return false
    }
  }

  // Copy file to Chatlo folder
  async copyFileToUploads(sourcePath: string, filename?: string): Promise<string> {
    const uploadsDir = path.join(this.chatloFolderPath, 'Uploads')
    const targetFilename = filename || path.basename(sourcePath)
    const targetPath = path.join(uploadsDir, targetFilename)

    // Ensure unique filename if file already exists
    let finalPath = targetPath
    let counter = 1
    while (fs.existsSync(finalPath)) {
      const ext = path.extname(targetFilename)
      const name = path.basename(targetFilename, ext)
      finalPath = path.join(uploadsDir, `${name}_${counter}${ext}`)
      counter++
    }

    fs.copyFileSync(sourcePath, finalPath)
    await this.indexFile(finalPath)
    return finalPath
  }

  // Save content as file in Chatlo folder
  async saveContentAsFile(content: string, filename: string, subfolder: string = 'Documents'): Promise<string> {
    const targetDir = path.join(this.chatloFolderPath, subfolder)
    const targetPath = path.join(targetDir, filename)

    fs.writeFileSync(targetPath, content, 'utf8')
    await this.indexFile(targetPath)
    return targetPath
  }

  // Delete file and remove from index
  async deleteFile(fileId: string): Promise<boolean> {
    try {
      const file = this.dbManager.getFile(fileId)
      if (!file) return false

      // Delete physical file if it exists
      if (fs.existsSync(file.filepath)) {
        fs.unlinkSync(file.filepath)
      }

      // Remove from database
      this.dbManager.deleteFile(fileId)
      console.log('Deleted file:', file.filename)
      return true
    } catch (error) {
      console.error('Error deleting file:', error)
      return false
    }
  }

  // Get file content
  getFileContent(filePath: string): Buffer | null {
    try {
      if (fs.existsSync(filePath)) {
        return fs.readFileSync(filePath)
      }
      return null
    } catch (error) {
      console.error('Error reading file:', error)
      return null
    }
  }

  // Check if file exists
  fileExists(filePath: string): boolean {
    return fs.existsSync(filePath)
  }
}
